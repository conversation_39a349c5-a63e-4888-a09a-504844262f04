{"v": "5.4.4", "fr": 30, "ip": 0, "op": 75, "w": 600, "h": 600, "assets": [], "layers": [{"ind": 1, "ty": 4, "sr": 1, "ks": {"o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [300, 277.5, 0]}, "a": {"a": 0, "k": [-99.28, -89.06, 0]}, "s": {"a": 1, "k": [{"i": {"x": [0.83, 0.83, 0.83], "y": [0.83, 0.83, 0.83]}, "o": {"x": [0.17, 0.17, 0.17], "y": [0.17, 0.17, 0.17]}, "t": 7, "s": [0, 0, 100], "e": [100, 100, 100]}, {"t": 32}], "x": "var $bm_rt;\nvar p = 0.81;\nvar a = 50;\nvar s = 1.70158;\nfunction easeandwizz_outElastic(t, b, c, d, a, p) {\n    if (t == 0)\n        return b;\n    if ((t /= d) == 1)\n        return $bm_sum(b, c);\n    if (!p)\n        p = $bm_mul(d, 0.3);\n    if (!a || a < Math.abs(c)) {\n        a = c;\n        var s = $bm_div(p, 4);\n    } else\n        var s = p / (2 * Math.PI) * Math.asin(c / a);\n    return $bm_sum($bm_sum($bm_mul($bm_mul(a, Math.pow(2, $bm_mul(-10, t))), Math.sin($bm_div($bm_mul($bm_sub($bm_mul(t, d), s), $bm_mul(2, Math.PI)), p))), c), b);\n}\nfunction easeAndWizz() {\n    var t, d, sX, eX, sY, eY, sZ, eZ, val1, val2, val2, val3;\n    var n = 0;\n    if (numKeys > 0) {\n        n = nearestKey(time).index;\n        if (key(n).time > time) {\n            n--;\n        }\n    }\n    try {\n        var key1 = key(n);\n        var key2 = key($bm_sum(n, 1));\n    } catch (e) {\n        return null;\n    }\n    var dim = 1;\n    try {\n        key(1)[1].length;\n        dim = 2;\n        key(1)[2].length;\n        dim = 3;\n    } catch (e) {\n    }\n    t = $bm_sub(time, key1.time);\n    d = $bm_sub(key2.time, key1.time);\n    sX = key1[0];\n    eX = $bm_sub(key2[0], key1[0]);\n    if (dim >= 2) {\n        sY = key1[1];\n        eY = $bm_sub(key2[1], key1[1]);\n        if (dim >= 3) {\n            sZ = key1[2];\n            eZ = $bm_sub(key2[2], key1[2]);\n        }\n    }\n    if (time < key1.time || time > key2.time) {\n        return value;\n    } else {\n        val1 = easeandwizz_outElastic(t, sX, eX, d, a, p, s);\n        switch (dim) {\n        case 1:\n            return val1;\n            break;\n        case 2:\n            val2 = easeandwizz_outElastic(t, sY, eY, d, a, p, s);\n            return [\n                val1,\n                val2\n            ];\n            break;\n        case 3:\n            val2 = easeandwizz_outElastic(t, sY, eY, d, a, p, s);\n            val3 = easeandwizz_outElastic(t, sZ, eZ, d, a, p, s);\n            return [\n                val1,\n                val2,\n                val3\n            ];\n            break;\n        default:\n            return null;\n        }\n    }\n}\n$bm_rt = easeAndWizz() || value;"}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[39.99, 51.92], [0, -51], [38.68, -50.23], [-115.39, -52.25]], "o": [[-38.68, -50.22], [0, -51], [-39.99, 51.92], [115.39, -52.25]], "v": [[15.54, -151.12], [-99.27, -120.71], [-214.1, -151.12], [-99.28, 44]], "c": true}}}, {"ty": "fl", "c": {"a": 1, "k": [{"i": {"x": [0.83], "y": [0.83]}, "o": {"x": [0.17], "y": [0.17]}, "t": 50, "s": [1, 0.45, 0.45, 1], "e": [0.89, 0.89, 0.89, 1]}, {"t": 75}]}, "o": {"a": 0, "k": 100}, "r": 1}, {"ty": "tr", "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}], "ip": 7, "op": 76, "st": 7}, {"ind": 2, "ty": 4, "td": 1, "sr": 1, "ks": {"o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [300, 277.5, 0]}, "a": {"a": 0, "k": [0, 0, 0]}, "s": {"a": 0, "k": [100, 100, 100]}}, "ao": 0, "shapes": [{"d": 1, "ty": "el", "s": {"a": 1, "k": [{"i": {"x": [0.67, 0.67], "y": [1, 1]}, "o": {"x": [0.33, 0.33], "y": [0, 0]}, "t": 6, "s": [10, 10], "e": [455, 455]}, {"t": 16}]}, "p": {"a": 0, "k": [0, 0]}}, {"ty": "fl", "c": {"a": 0, "k": [0, 0.188, 1, 1]}, "o": {"a": 0, "k": 100}, "r": 1}], "ip": 6, "op": 76, "st": 6}, {"ind": 3, "ty": 4, "tt": 2, "sr": 1, "ks": {"o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [300, 277.5, 0]}, "a": {"a": 0, "k": [0, 0, 0]}, "s": {"a": 0, "k": [100, 100, 100]}}, "ao": 0, "shapes": [{"d": 1, "ty": "el", "s": {"a": 1, "k": [{"i": {"x": [0.67, 0.67], "y": [1, 1]}, "o": {"x": [0.33, 0.33], "y": [0, 0]}, "t": 5, "s": [0, 0], "e": [450, 450]}, {"t": 15}]}, "p": {"a": 0, "k": [0, 0]}}, {"ty": "fl", "c": {"a": 0, "k": [1, 0.455, 0.455, 1]}, "o": {"a": 0, "k": 100}, "r": 1}], "ip": 5, "op": 76, "st": 5}, {"ind": 4, "ty": 4, "td": 1, "sr": 1, "ks": {"o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [300, 277.5, 0]}, "a": {"a": 0, "k": [0, 0, 0]}, "s": {"a": 0, "k": [100, 100, 100]}}, "ao": 0, "shapes": [{"d": 1, "ty": "el", "s": {"a": 1, "k": [{"i": {"x": [0.67, 0.67], "y": [1, 1]}, "o": {"x": [0.33, 0.33], "y": [0, 0]}, "t": 5, "s": [0, 0], "e": [450, 450]}, {"t": 15}]}, "p": {"a": 0, "k": [0, 0]}}, {"ty": "fl", "c": {"a": 0, "k": [1, 0.455, 0.455, 1]}, "o": {"a": 0, "k": 100}, "r": 1}], "ip": 5, "op": 76, "st": 5}, {"ind": 5, "ty": 4, "tt": 2, "sr": 1, "ks": {"o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [300, 300, 0]}, "a": {"a": 0, "k": [-99.28, -66.53, 0]}, "s": {"a": 0, "k": [100, 100, 100]}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[39.99, 51.92], [0, -51], [38.68, -50.23], [-115.39, -52.25]], "o": [[-38.68, -50.22], [0, -51], [-39.99, 51.92], [115.39, -52.25]], "v": [[15.54, -151.12], [-99.27, -120.71], [-214.1, -151.12], [-99.28, 44]], "c": true}}}, {"ty": "fl", "c": {"a": 0, "k": [0.886, 0.886, 0.886, 1]}, "o": {"a": 0, "k": 100}, "r": 1}, {"ty": "tr", "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}], "ip": 0, "op": 76, "st": 0}, {"ind": 6, "ty": 4, "sr": 1, "ks": {"o": {"a": 0, "k": 100}, "r": {"a": 0, "k": -4}, "p": {"a": 0, "k": [300, 300, 0]}, "a": {"a": 0, "k": [0, 0, 0]}, "s": {"a": 0, "k": [100, 100, 100]}}, "ao": 0, "shapes": [{"d": 1, "ty": "el", "s": {"a": 1, "k": [{"i": {"x": [0.67, 0.67], "y": [1, 1]}, "o": {"x": [0.33, 0.33], "y": [0, 0]}, "t": 23, "s": [10, 10], "e": [0, 0]}, {"t": 36}]}, "p": {"a": 1, "k": [{"i": {"x": 0.44, "y": 1}, "o": {"x": 0.33, "y": 0}, "t": 2, "s": [0, 0], "e": [0, 226], "to": [0, 37.67], "ti": [0, -37.67]}, {"t": 36}]}}, {"ty": "fl", "c": {"a": 0, "k": [1, 0.455, 0.455, 1]}, "o": {"a": 0, "k": 100}, "r": 1}, {"ty": "rp", "c": {"a": 0, "k": 6}, "o": {"a": 0, "k": 0}, "m": 1, "tr": {"p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 60}, "so": {"a": 0, "k": 100}, "eo": {"a": 0, "k": 100}}}], "ip": 11, "op": 76, "st": 2}, {"ind": 7, "ty": 4, "sr": 1, "ks": {"o": {"a": 0, "k": 100}, "r": {"a": 0, "k": -22}, "p": {"a": 0, "k": [300, 300, 0]}, "a": {"a": 0, "k": [0, 0, 0]}, "s": {"a": 0, "k": [100, 100, 100]}}, "ao": 0, "shapes": [{"d": 1, "ty": "el", "s": {"a": 1, "k": [{"i": {"x": [0.67, 0.67], "y": [1, 1]}, "o": {"x": [0.33, 0.33], "y": [0, 0]}, "t": 22, "s": [20, 20], "e": [0, 0]}, {"t": 35}]}, "p": {"a": 1, "k": [{"i": {"x": 0.44, "y": 1}, "o": {"x": 0.33, "y": 0}, "t": 1, "s": [0, 0], "e": [0, 246], "to": [0, 41], "ti": [0, -41]}, {"t": 35}]}}, {"ty": "fl", "c": {"a": 0, "k": [1, 0.455, 0.455, 1]}, "o": {"a": 0, "k": 100}, "r": 1}, {"ty": "rp", "c": {"a": 0, "k": 6}, "o": {"a": 0, "k": 0}, "m": 1, "tr": {"p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 60}, "so": {"a": 0, "k": 100}, "eo": {"a": 0, "k": 100}}}], "ip": 11, "op": 76, "st": 1}], "markers": []}