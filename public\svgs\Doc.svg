<svg width="135" height="183" viewBox="0 0 135 183" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M9.81818 183H122.182C127.604 183 132 178.604 132 173.182V47.9759C132 45.3719 130.966 42.8747 129.124 41.0334L93.9666 5.87568C92.1251 4.03441 89.628 3 87.024 3H9.81818C4.39575 3 0 7.39575 0 12.8182V173.182C0 178.604 4.39575 183 9.81818 183Z" fill="#424242"/>
<g filter="url(#filter0_d_36_10)">
<path d="M93.2727 5.18182L129.273 41.1818H103.091C97.6686 41.1818 93.2727 36.7861 93.2727 31.3636V5.18182Z" fill="#161616"/>
</g>
<defs>
<filter id="filter0_d_36_10" x="88.2727" y="0.181824" width="46" height="46" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="2.5"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_36_10"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_36_10" result="shape"/>
</filter>
</defs>
</svg>
