<!DOCTYPE html>
<html>
<head><title>API Test</title></head>
<body>
    <button onclick="testGoogleAPI()">Test Google API</button>
    <div id="results"></div>
    
    <script>
        async function testGoogleAPI() {
            try {
                const response = await fetch('/api/debug-env', {method: 'POST'});
                const data = await response.json();
                document.getElementById('results').innerHTML = JSON.stringify(data, null, 2);
            } catch (error) {
                document.getElementById('results').innerHTML = 'Error: ' + error.message;
            }
        }
    </script>
</body>
</html>