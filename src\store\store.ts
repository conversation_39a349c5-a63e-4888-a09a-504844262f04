import { configureStore, combineReducers } from "@reduxjs/toolkit";
import {
  persistStore,
  persistReducer,
  FLUSH,
  REHYDRATE,
  PAUSE,
  PERSIST,
  PURGE,
  REGISTER,
} from "redux-persist";
import storage from "redux-persist/lib/storage";
import chatReducer from "./chatSlice";
import authReducer from "./authSlice";
import aiReducer from "./aiSlice";

const persistConfig = {
  key: "root",
  version: 1,
  storage,
  whitelist: ["chat", "auth", "ai"],
};

const rootReducer = combineReducers({
  chat: chatReducer,
  auth: authReducer,
  ai: aiReducer,
});

const persistedReducer = persistReducer(persistConfig, rootReducer);

export const store = configureStore({
  reducer: persistedReducer,
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({
      serializableCheck: {
        ignoredActions: [FLUSH, REHYDRAT<PERSON>, PAUSE, <PERSON>ERSIST, <PERSON>UR<PERSON>, R<PERSON><PERSON><PERSON>R],
      },
    }),
});

export const persistor = persistStore(store);

export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;
