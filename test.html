<!DOCTYPE html>
<html>
<head>
    <title>API Test</title>
</head>
<body>
    <h1>API Tests</h1>
    <button onclick="testDebug()">Test Debug</button>
    <button onclick="testTools()">Test Tools</button>
    <div id="results"></div>

    <script>
        function log(message) {
            document.getElementById('results').innerHTML += '<p>' + JSON.stringify(message) + '</p>';
        }

        async function testDebug() {
            try {
                const response = await fetch('/api/debug-env', {method: 'POST'});
                const data = await response.json();
                log('Debug: ' + JSON.stringify(data));
            } catch (error) {
                log('Debug Error: ' + error.message);
            }
        }

        async function testTools() {
            try {
                const response = await fetch('/api/tools', {
                    method: 'POST',
                    headers: {'Content-Type': 'application/json'},
                    body: JSON.stringify([{role: 'user', content: 'test'}])
                });
                const data = await response.json();
                log('Tools: ' + JSON.stringify(data));
            } catch (error) {
                log('Tools Error: ' + error.message);
            }
        }
    </script>
</body>
</html>