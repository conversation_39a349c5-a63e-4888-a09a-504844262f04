.container {
  position: fixed;
  height: 100vh;
  width: 326px;
  top: 0;
  left: 0;
  background-color: #232323;
  display: flex;
  flex-direction: row;
  z-index: 10;
  transition: all 1s;
}
.opening {
  animation: slideInFromLeft 300ms ease-in-out forwards;
}
.closing {
  animation: slideOutToLeft 300ms ease-in-out forwards;
}
.header {
  position: fixed;
  width: 100%;
  height: auto;
  padding: 6px 12px;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: flex-start;
  z-index: 2;
}
.menu {
  margin-top: 6px;
  cursor: pointer;
}
.titleButton {
  margin: 8px;
  padding: 6px 12px;
  border-radius: 8px;
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  background-color: #2e2e2e;
  transition: all 0.25s ease-in-out;
}
.titleButton:hover {
  background-color: #ffffff14;
}
.titleButtonIcon {
  margin-right: 8px;
}
.titleButtonText {
  font-size: 14px;
  line-height: 20px;
  letter-spacing: -0.008em;
  font-weight: 500;
  color: #ffffff;
}
.barContainer {
  min-width: 70px;
  height: 100%;
  padding: 8px;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: center;
  border-right: 1px solid #ffffff14;
  background-color: #232323;
}
.logo {
  width: 36px;
  height: 36px;
  margin-top: 8px;
  margin-bottom: 24px;
}
.iconContainer {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  align-items: center;
}
.icon {
  width: 42px;
  height: 42px;
  padding: 4px;
  margin-bottom: 8px;
  cursor: pointer;
  transition: all 0.25s ease-in-out;
}
.iconActive {
  width: 42px;
  height: 42px;
  padding: 4px;
  margin-bottom: 8px;
  border-radius: 6px;
  cursor: pointer;
  background-color: #ffffff14;
  transition: all 0.25s ease-in-out;
}
.icon:hover {
  background-color: #ffffff10;
  border-radius: 6px;
}
.mainContainer {
  width: 256px;
  height: 100%;
  padding: 8px;
  background-color: #232323;
}
.list {
  width: 100%;
  height: auto;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: flex-start;
}
.titleContainer {
  width: 100%;
  height: 36px;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  margin-top: 10px;
  padding: 0px 4px;
}
.title {
  font-size: 21px;
  line-height: 24px;
  font-weight: 500;
  color: #e8e8e6;
}
.titleButtonRow {
  gap: 8px;
  display: flex;
  flex-direction: row;
  justify-content: flex-end;
  align-items: center;
  transition: all 0.25s ease-in-out;
}
.titleButton {
  padding: 6px 12px;
  border-radius: 8px;
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  transition: all 0.25s ease-in-out;
  background-color: #2e2e2e;
}
.titleButton:hover {
  background-color: #ffffff14;
}
.titleButtonIcon {
  width: 24px;
  height: 24px;
  margin-right: 8px;
}
.titleButtonText {
  font-size: 14px;
  line-height: 20px;
  letter-spacing: -0.008em;
  font-weight: 500;
  color: #ffffff;
}
.listContainer {
  width: 100%;
  height: 100%;
  overflow: scroll;
  margin-top: 12px;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: flex-start;
  padding-bottom: 16px;
  scrollbar-width: none;
}
.emptyState {
  flex: 1;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}
.emptyStateIcon {
  width: 120px;
  height: 120px;
}
.emptyStateText {
  width: 80%;
  margin-top: 16px;
  font-size: 16px;
  line-height: 24px;
  font-weight: 500;
  color: #e8e8e6aa;
  text-align: center;
}
.listHeader {
  font-size: 14px;
  line-height: 16px;
  font-weight: 500;
  color: #e8e8e6aa;
  padding: 8px 12px;
  margin-top: 4px;
  margin-bottom: 4px;
  cursor: pointer;
}
.listItem {
  width: 100%;
  padding: 8px 12px;
  font-size: 14px;
  line-height: 16px;
  font-weight: 400;
  color: #e8e8e6;
  cursor: pointer;
  border-radius: 6px;
  transition: all 0.25s ease-in-out;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
}
.listItem:hover {
  background-color: #ffffff14;
}
.listItem:hover .bin {
  opacity: 1;
}
.bin {
  width: 18px;
  height: 18px;
  opacity: 0;
  transition: opacity 0.25s ease-in-out;
}
.listRow {
  width: 100%;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
}
.selector {
  width: 100%;
  padding: 4px 12px;
}
.slider {
  width: 100%;
  padding: 4px 12px;
}
.thumb {
  cursor: grab;
  width: 16px;
  height: 16px;
  border-radius: 50%;
  margin-top: 2px;
  background-color: #e8e8e6;
}
.thumb:active {
  cursor: grabbing;
}
.textareaContainer {
  width: 100%;
  padding: 0px 12px;
}
.textarea {
  width: 100%;
  height: 256px;
  padding: 8px 12px;
  font-size: 14px;
  line-height: 20px;
  font-weight: 400;
  color: #e8e8e6;
  background-color: #2e2e2e;
  border-radius: 8px;
  resize: none;
}
.textarea:focus {
  outline: none;
}

@media (min-width: 513px) {
  .mobileModal {
    display: none;
  }
}

@media (max-width: 512px) {
  .header {
    padding: 12px 16px;
    align-items: center;
    background-color: #232323;
  }
  .menu {
    margin-top: 0px;
    cursor: pointer;
  }
  .titleButton {
    margin: 0px;
    padding: 0px;
    display: flex;
    flex-direction: row;
    justify-content: center;
    align-items: center;
  }
  .titleButton:hover {
    background-color: transparent;
  }
  .titleButtonIcon {
    margin-right: 0px;
  }
  .titleButtonText {
    display: none;
  }
}

.mobileOverlay {
  position: fixed;
  top: 0;
  right: 0;
  width: 100%;
  height: 100%;
  z-index: 2;
  background-color: rgba(0, 0, 0, 0.25);
  transition: all 1s;
  animation: fadeInFromNone 300ms ease-in-out;
}
.mobileOverlayClosing {
  animation: fadeOutToNone 300ms ease-in-out forwards;
}

@keyframes slideInFromLeft {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(0);
  }
}

@keyframes slideOutToLeft {
  0% {
    transform: translateX(0);
  }
  100% {
    transform: translateX(-100%);
  }
}

@keyframes fadeInFromNone {
  0% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}

@keyframes fadeOutFromNone {
  0% {
    opacity: 1;
  }
  100% {
    opacity: 0;
  }
}

.pricingSection {
  margin: 8px 0;
  padding: 0;
}

.pricingButton {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 42px;
  height: 42px;
  margin: 0 auto 8px auto;
  border-radius: 6px;
  background-color: transparent;
  color: #ffffff;
  text-decoration: none;
  cursor: pointer;
  transition: all 0.25s ease-in-out;
  border: 1px solid #ffffff14;
}

.pricingButton:hover {
  background-color: #ffffff14;
}

.pricingContent {
  display: flex;
  align-items: center;
  justify-content: center;
}

.pricingIcon {
  font-size: 20px;
  color: #fbbf24;
}

.pricingText {
  display: none;
}
