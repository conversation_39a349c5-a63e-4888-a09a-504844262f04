.container {
  width: 100%;
  min-height: 100vh;
  overflow-y: scroll;
  padding-top: 72px;
  padding-bottom: 120px;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: center;
  animation: fadeInFromNone 1000ms ease-in-out;
  transition: all 1s;
  scroll-behavior: smooth;
}
@keyframes fadeInFromNone {
  0% {
    display: none;
    opacity: 0;
  }
  1% {
    display: flex;
    opacity: 0;
  }
  100% {
    display: flex;
    opacity: 1;
  }
}
.chat {
  width: 40%;
  min-width: 512px;
}
.question {
  font-style: normal;
  font-size: 24px;
  line-height: 32px;
  color: #e8e8e6;
}
.divider {
  width: 100%;
  height: 1px;
  background-color: #ffffff14;
  margin-top: 36px;
  margin-bottom: 36px;
}
@media (max-width: 512px) {
  .container {
    padding-top: 72px;
  }
  .chat {
    min-width: 100%;
  }
  .question {
    margin-left: 5%;
    margin-right: 5%;
  }
  .divider {
    width: 90%;
    margin-left: 5%;
  }
}
