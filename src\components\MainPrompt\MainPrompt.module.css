.container {
  padding: 0rem;
  width: 100%;
  max-height: 100vh;
  background-color: #161616;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  padding: 80px 16px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  animation: fadeInFromNone 1000ms ease-in-out;
  transition: all 1s;
}
@keyframes fadeInFromNone {
  0% {
    display: none;
    opacity: 0;
  }
  1% {
    display: flex;
    opacity: 0;
  }
  100% {
    display: flex;
    opacity: 1;
  }
}
.title {
  font-style: normal;
  font-weight: 300;
  font-size: 36px;
  line-height: 40px;
  margin-bottom: 12px;
  color: #ffffff;
  justify-content: center;
  align-items: center;
  text-align: center;
}
.promptContainer {
  width: 40%;
  flex-grow: 1;
  overflow: auto;
  min-width: 512px;
  margin-top: 24px;
  border-radius: 8px;
  padding: 8px;
  background-color: #232323;
  border: 1px solid #ffffff14;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  align-items: center;
}
.promptText {
  width: 100%;
  resize: none;
  overflow-y: auto;
  min-height: 56px;
  max-height: 512px;
  font-size: 18px;
  color: #ffffff;
  background-color: transparent;
  border-width: 0px;
  margin-left: 4px;
  margin-right: 8px;
  margin-bottom: 8px;
  padding: 4px 8px;
}
.promptText:focus {
  outline: none;
  outline-color: transparent;
  box-shadow: none !important;
}
.mainRow {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}
.sectionRow {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  gap: 12px;
}
.button {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  gap: 6px;
  opacity: 0.7;
  cursor: pointer;
  padding: 3px 9px;
  border-radius: 4px;
  background-color: transparent;
}
.button:hover {
  opacity: 1;
  background-color: #161616aa;
}
.buttonText {
  width: 100%;
  font-size: 16px;
  color: #ffffffaa;
  background-color: transparent;
}
.sendButton {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 32px;
  height: 32px;
  border-radius: 6px;
  background-color: #ffffff;
  cursor: pointer;
  transition: all 0.2s;
}
.button:hover .cross {
  display: flex;
}
.cross {
  display: none;
  width: 18px;
  height: 18px;
}
.spinner {
  width: 18px;
  height: 18px;
  scale: 0.75;
}
.popoverContainer {
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  background-color: #171717;
  border: solid 1px #ffffff14;
}
.popover {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  row-gap: 4px;
  column-gap: 4px;
}
.popoverBlock {
  max-width: 200px;
  padding: 4px;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: flex-start;
  transition: all 0.25s ease-in-out;
}
.popoverBlock:hover {
  background-color: #ffffff14;
  border-radius: 4px;
}
.popoverTitleContainer {
  padding-left: 2px;
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  align-items: center;
  gap: 0px;
}
.popoverText {
  font-size: 16px;
  color: #ffffff;
  padding: 4px;
  cursor: pointer;
  transition: all 0.2s;
}
.popoverSmallText {
  width: 80%;
  font-size: 12px;
  color: #ffffffaa;
  padding: 4px;
  padding-top: 0px;
  cursor: pointer;
  transition: all 0.2s;
}
.modal {
  width: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  margin-bottom: 16px;
}
.modalBlock {
  width: 100%;
  padding: 8px 16px;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: flex-start;
}
.modalRow {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}
.modalTitleContainer {
  padding-left: 2px;
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  align-items: center;
  gap: 0px;
}
.modalText {
  font-size: 16px;
  color: #ffffff;
  padding: 4px;
  cursor: pointer;
  transition: all 0.2s;
}
.modalSmallText {
  width: 100%;
  font-size: 14px;
  color: #ffffffaa;
  padding: 4px;
  padding-top: 0px;
  cursor: pointer;
  transition: all 0.2s;
}
.divider {
  width: 90%;
  height: 1px;
  background-color: #ffffff14;
}
@media (max-width: 512px) {
  .container {
    padding: 16px;
  }
  .title {
    font-size: 27px;
    line-height: 32px;
    margin-bottom: 8px;
  }
  .promptContainer {
    overflow: hidden;
    min-width: 100%;
    max-width: 512px;
    margin-top: 24px;
    border-radius: 8px;
  }
}
