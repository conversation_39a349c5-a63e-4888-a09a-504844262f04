{"name": "omniplex", "version": "1.0.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@firebase/firestore": "^4.5.0", "@firebase/storage": "^0.12.2", "@headlessui/react": "^2.0.3", "@headlessui/tailwindcss": "^0.2.0", "@heroicons/react": "^2.1.1", "@lottiefiles/react-lottie-player": "^3.5.3", "@nextui-org/react": "^2.2.10", "@reduxjs/toolkit": "^2.2.3", "@remixicon/react": "^4.2.0", "@stripe/stripe-js": "^7.6.1", "@tremor/react": "^3.16.3", "@types/react-redux": "^7.1.33", "@vercel/analytics": "^1.2.2", "@vercel/og": "^0.6.2", "@vercel/speed-insights": "^1.0.10", "ai": "^3.0.12", "dotenv": "^17.2.1", "firebase": "^10.9.0", "formidable": "^3.5.1", "framer-motion": "^11.0.15", "googleapis": "^134.0.0", "katex": "^0.16.10", "nanoid": "^5.0.6", "next": "14.1.3", "openai": "^4.29.1", "react": "^18", "react-dom": "^18", "react-firebase-hooks": "^5.1.1", "react-hot-toast": "^2.4.1", "react-markdown": "^9.0.1", "react-modal-sheet": "^2.2.0", "react-redux": "^9.1.0", "react-syntax-highlighter": "^15.5.0", "redux-persist": "^6.0.0", "rehype-katex": "^7.0.0", "rehype-raw": "^7.0.0", "rehype-sanitize": "^6.0.0", "stripe": "^18.3.0", "swiper": "^11.1.1"}, "devDependencies": {"@tailwindcss/forms": "^0.5.7", "@tailwindcss/typography": "^0.5.10", "@types/formidable": "^3.4.5", "@types/mixpanel-browser": "^2.49.0", "@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "@types/react-syntax-highlighter": "^15.5.11", "@types/stripe": "^8.0.416", "autoprefixer": "^10.0.1", "eslint": "^8", "eslint-config-next": "14.1.3", "postcss": "^8", "postcss-import": "^16.1.0", "postcss-nesting": "^12.1.1", "tailwindcss": "^3.3.0", "typescript": "^5"}}