.list {
  width: 100%;
  height: auto;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: flex-start;
}
.modalOverlay {
  position: fixed;
  bottom: 0;
  left: 70px;
  width: 255px;
  height: 92%;
  z-index: 1;
  display: flex;
  justify-content: center;
  align-items: flex-end;
  background: linear-gradient(to bottom, #232323aa, #161616);
}
.modalButton {
  cursor: pointer;
  width: 90%;
  height: 40px;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 16px;
  line-height: 18px;
  font-weight: 500;
  text-align: center;
  color: #151515;
  border-radius: 8px;
  margin-bottom: 12px;
  background-color: #fff;
}
.titleContainer {
  width: 100%;
  height: 36px;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  margin-top: 10px;
  padding: 0px 4px;
}
.title {
  font-size: 21px;
  line-height: 24px;
  font-weight: 500;
  color: #e8e8e6;
}
.titleButtonRow {
  gap: 8px;
  display: flex;
  flex-direction: row;
  justify-content: flex-end;
  align-items: center;
  transition: all 0.25s ease-in-out;
}
.titleButton {
  padding: 6px 12px;
  border-radius: 8px;
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  transition: all 0.25s ease-in-out;
  background-color: #2e2e2e;
}
.titleButton:hover {
  background-color: #ffffff14;
}
.titleButtonText {
  font-size: 14px;
  line-height: 20px;
  letter-spacing: -0.008em;
  font-weight: 500;
  color: #ffffff;
}
.listContainer {
  width: 100%;
  height: 100%;
  overflow: scroll;
  margin-top: 12px;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: flex-start;
  padding-bottom: 16px;
  scrollbar-width: none;
}
.listHeader {
  font-size: 14px;
  line-height: 16px;
  font-weight: 500;
  color: #e8e8e6aa;
  padding: 8px 12px;
  margin-top: 4px;
  margin-bottom: 4px;
  cursor: pointer;
}
.listRow {
  width: 100%;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
}
.tooltip {
  border-radius: 8px;
  cursor: pointer;
}
.tooltipIcon {
  margin-right: 12px;
  cursor: pointer;
  opacity: 0.75;
}
.selector {
  width: 100%;
  padding: 4px 12px;
}
.slider {
  width: 100%;
  padding: 4px 12px;
}
.thumb {
  cursor: grab;
  width: 16px;
  height: 16px;
  border-radius: 50%;
  margin-top: 2px;
  background-color: #e8e8e6;
}
.thumb:active {
  cursor: grabbing;
}
.textareaContainer {
  width: 100%;
  padding: 0px 12px;
}
.textarea {
  width: 100%;
  height: 256px;
  padding: 8px 12px;
  font-size: 14px;
  line-height: 20px;
  font-weight: 400;
  color: #e8e8e6;
  background-color: #2e2e2e;
  border-radius: 8px;
  resize: none;
}
.textarea:focus {
  outline: none;
}
