.container {
  position: absolute;
  top: -50%;
  right: -56%;
  width: 360px;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: flex-start;
  gap: 16px;
  cursor: pointer;
}
.card {
  width: 100%;
  min-height: 45px;
  border-radius: 8px;
  border: 1px dashed #ffffff14;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
}
.cardTitle {
  font-style: normal;
  font-size: 16px;
  line-height: 21px;
  color: #e8e8e6aa;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  gap: 12px;
}
.icon {
  width: 21px;
  height: 21px;
}
.plus {
  width: 18px;
  height: 18px;
}
.carousel {
  width: 100%;
  height: 200px;
  overflow: hidden;
  border-radius: 8px;
  border: 1px solid #ffffff14;
}
.swiper {
  width: 100%;
  height: 100%;
}
.swiper img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}
.prevArrow,
.nextArrow {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  z-index: 10;
  cursor: pointer;
}
.prevArrow {
  left: 10px;
}
.nextArrow {
  right: 10px;
}
.videoSlide {
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
}
.videoSlide img {
  max-width: 100%;
  max-height: 100%;
  object-fit: cover;
}
.playIcon {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  cursor: pointer;
  width: 50px;
  height: 50px;
}
@media (max-width: 1700px) {
  .container {
    position: relative;
    top: 0px;
    right: 0px;
    width: 100%;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    align-items: flex-start;
    gap: 16px;
    margin-top: 16px;
  }
}
@media (max-width: 512px) {
  .container {
    position: relative;
    top: 0px;
    right: 0px;
    width: 90%;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    align-items: flex-start;
    gap: 16px;
    margin-top: 16px;
    margin-left: 5%;
  }
}
