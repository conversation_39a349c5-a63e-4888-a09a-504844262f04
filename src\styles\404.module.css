.container {
  padding: 0rem;
  max-height: 100vh;
  background-color: #161616 !important;
}
.textContainer {
  width: 100%;
  height: 100%;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  padding: 80px 16px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  background-color: #161616 !important;
}
.title {
  font-style: normal;
  font-weight: 700;
  font-size: 80px;
  line-height: 72px;
  color: #ffffff;
  justify-content: center;
  align-items: center;
  text-align: center;
}
.text {
  padding: 24px 0px;
  font-style: normal;
  font-weight: 600;
  font-size: 18px;
  line-height: 24px;
  color: #ededed;
  justify-content: center;
  align-items: center;
  text-align: center;
}
.subText {
  width: 70%;
  font-style: normal;
  font-weight: 500;
  font-size: 16px;
  line-height: 21px;
  color: #989898;
  justify-content: center;
  align-items: center;
  text-align: center;
}
.bottomContainer {
  display: flex;
  justify-content: center;
  align-items: center;
  width: auto;
  height: 50px;
  position: fixed;
  background-color: #232323;
  box-shadow: 0 30px 60px #0000001f;
  backdrop-filter: blur(100px) saturate(400%) brightness(100%);
  border: 1px solid #ffffff14;
  bottom: -4px;
  left: 50%;
  border-radius: 12px;
  z-index: 10;
  transform: translate(-50%, -50%) translateY(80px);
  animation: 500ms ease 200ms 1 normal forwards running footer;
  transition: all 1s;
  cursor: pointer;
}
.promptContainer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  padding: 8px;
}
.promptText {
  width: 100%;
  height: 27px;
  font-size: 18px;
  color: #ffffff;
  background-color: transparent;
  border-width: 0px;
  margin-left: 4px;
  margin-right: 8px;
}
.promptText:focus {
  outline: none;
}
.sendButton {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 50px;
  height: 32px;
  border-radius: 6px;
  background-color: #ffffff;
  cursor: pointer;
  transition: all 0.2s;
}

@keyframes footer {
  0% {
    opacity: 0;
    transform: translate(-50%, -50%) translateY(80px);
  }
  100% {
    opacity: 1;
    transform: translate(-50%, -50%) translateY(0px);
  }
}
